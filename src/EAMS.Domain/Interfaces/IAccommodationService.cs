using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<Accommodation>> GetAll();
    Task<Accommodation?> GetById(Int64 id);
    Task<Accommodation> Create(Accommodation accommodation);
    Task<Accommodation> Update(Accommodation accommodation);
    Task<bool> Delete(Int64 id);
    Task<IEnumerable<Accommodation>> SearchAccommodations(string searchTerm);
}
