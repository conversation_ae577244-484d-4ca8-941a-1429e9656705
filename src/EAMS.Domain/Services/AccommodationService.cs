using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;

namespace EAMS.Domain.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;

    public AccommodationService(IAccommodationRepository accommodationRepository)
    {
        _accommodationRepository = accommodationRepository;
    }

    public async Task<IEnumerable<Accommodation>> GetAll()
    {
        return await _accommodationRepository.GetAllAsync();
    }

    public async Task<Accommodation?> GetById(Int64 id)
    {
        return await _accommodationRepository.GetByIdAsync(id);
    }

    public async Task<Accommodation> Create(Accommodation accommodation)
    {
        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _accommodationRepository.AddAsync(accommodation);

        // Return the accommodation with its generated ID
        return accommodation;
    }

    public async Task<Accommodation> Update(Accommodation accommodation)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodation.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodation.Id);
        }

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _accommodationRepository.UpdateAsync(accommodation);

        return accommodation;
    }

    public async Task<bool> Delete(Int64 id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _accommodationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<IEnumerable<Accommodation>> SearchAccommodations(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetAll();
        }

        var searchTermLower = searchTerm.ToLower();
        return await _accommodationRepository.GetAllAsync(a =>
            a.Name.ToLower().Contains(searchTermLower) ||
            a.StreetLine1.ToLower().Contains(searchTermLower) ||
            (a.StreetLine2 != null && a.StreetLine2.ToLower().Contains(searchTermLower)) ||
            a.Suburb.ToLower().Contains(searchTermLower) ||
            a.Postcode.ToLower().Contains(searchTermLower)
        );
    }
}
