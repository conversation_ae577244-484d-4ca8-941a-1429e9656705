using System.Linq.Expressions;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Services;
using Moq;
using Xunit;

namespace EAMS.Tests;

public class AccommodationServiceTestFixture
{
    private Mock<IAccommodationRepository> _mockAccommodationRepository;
    private AccommodationService _targetService;
    private List<Accommodation> _testAccommodations;

    public AccommodationServiceTestFixture()
    {
        _mockAccommodationRepository = new Mock<IAccommodationRepository>();
        _targetService = new AccommodationService(_mockAccommodationRepository.Object);

        // Setup test data
        _testAccommodations = new List<Accommodation>
        {
            new Accommodation
            {
                Id = 1,
                Name = "Grand Hotel Melbourne",
                StreetLine1 = "123 Collins Street",
                StreetLine2 = null,
                Suburb = "Melbourne",
                State = "VIC",
                Postcode = "3000",
                Region = Region.MelbourneCBD,
                AccommodationType = AccommodationType.Hotel,
                Density = Density.High,
                Duration = new List<Duration> { Duration.ShortTermStay }
            },
            new Accommodation
            {
                Id = 2,
                Name = "Cozy Motel",
                StreetLine1 = "456 Main Street",
                StreetLine2 = "Unit 5",
                Suburb = "Richmond",
                State = "VIC",
                Postcode = "3121",
                Region = Region.InnerEasternMelbourne,
                AccommodationType = AccommodationType.MotelOrMotorInn,
                Density = Density.Medium,
                Duration = new List<Duration> { Duration.LongTermStay }
            },
            new Accommodation
            {
                Id = 3,
                Name = "Seaside Apartments",
                StreetLine1 = "789 Beach Road",
                StreetLine2 = null,
                Suburb = "St Kilda",
                State = "VIC",
                Postcode = "3182",
                Region = Region.BaysidePeninsula,
                AccommodationType = AccommodationType.ServicedApartment,
                Density = Density.Low,
                Duration = new List<Duration> { Duration.SingleTermStay, Duration.ShortTermStay }
            }
        };
    }

    [Fact]
    public async Task SearchAccommodations_WithEmptySearchTerm_ShouldReturnAllAccommodations()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(null))
            .ReturnsAsync(_testAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("");

        // Assert
        Assert.Equal(3, result.Count());
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(null), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_WithNullSearchTerm_ShouldReturnAllAccommodations()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(null))
            .ReturnsAsync(_testAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations(null);

        // Assert
        Assert.Equal(3, result.Count());
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(null), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_ByName_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Name.ToLower().Contains("hotel")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("hotel");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Name == "Grand Hotel Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_ByStreetAddress_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.StreetLine1.ToLower().Contains("main")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("main");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.StreetLine1 == "456 Main Street");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_BySuburb_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Suburb.ToLower().Contains("melbourne")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("melbourne");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Suburb == "Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_ByPostcode_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Postcode.ToLower().Contains("3000")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("3000");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Postcode == "3000");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_CaseInsensitive_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Name.ToLower().Contains("hotel")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.SearchAccommodations("HOTEL");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Name == "Grand Hotel Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task SearchAccommodations_NoMatches_ShouldReturnEmptyList()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(new List<Accommodation>());

        // Act
        var result = await _targetService.SearchAccommodations("nonexistent");

        // Assert
        Assert.Empty(result);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }
}
